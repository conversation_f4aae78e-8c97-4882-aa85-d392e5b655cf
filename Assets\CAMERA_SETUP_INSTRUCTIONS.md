# Camera System Setup Instructions

This guide will help you set up the first person and third person camera system for your running game.

## What's Included

1. **CameraController.cs** - Main script that handles camera switching and first person mouse look
2. **CameraSetupHelper.cs** - Helper script to automatically configure your cameras
3. **Updated PlayerMovement.cs** - Integrated with the camera system

## Setup Steps

### Step 1: Add Scripts to Your Player
1. Select your Player GameObject in the hierarchy
2. Add the `CameraController` component to your Player
3. The PlayerMovement script has been updated to work with the camera system

### Step 2: Set Up Cameras Automatically
1. Create an empty GameObject in your scene and name it "Camera Setup Helper"
2. Add the `CameraSetupHelper` script to this GameObject
3. In the inspector, assign your Player GameObject to the "Player Transform" field
4. Right-click on the CameraSetupHelper component and select "Setup Cameras"
5. This will automatically:
   - Create or configure a "1st Person Camera" as a child of your player
   - Rename and configure your existing "FreeLook Camera" to "3rd Person Camera"
   - Ensure your Main Camera has a CinemachineBrain component

### Step 3: Manual Camera Assignment (if needed)
If the automatic setup doesn't work perfectly:

1. Select your Player GameObject
2. In the CameraController component, manually assign:
   - **First Person Camera**: The camera that should be a child of your player
   - **Third Person Camera**: Your existing FreeLook camera (now renamed to "3rd Person Camera")

### Step 4: Configure Settings
In the CameraController component on your Player, you can adjust:
- **Mouse Sensitivity**: How fast the camera rotates with mouse movement
- **Vertical Look Limit**: Maximum up/down look angle (default 90 degrees)
- **Switch Camera Key**: Key to switch between cameras (default: C key)

## Controls

- **Mouse Movement**: Look around (only in first person mode)
- **C Key**: Switch between first person and third person cameras
- **Y Button (Gamepad)**: Switch cameras when using a controller

## How It Works

### First Person Mode
- Camera is attached to the player and moves with them
- Mouse input rotates the player horizontally and the camera vertically
- Cursor is locked and hidden
- Provides direct control for precise aiming

### Third Person Mode
- Camera follows the player from behind/side
- Uses Cinemachine's third person follow system
- Cursor is visible and free
- Better for navigation and situational awareness

### Camera Switching
- Uses Cinemachine priorities to switch between cameras
- Active camera has priority 10, inactive camera has priority 0
- Smooth blending between cameras (1 second transition)

## Troubleshooting

### Camera Not Switching
- Make sure both cameras are properly assigned in the CameraController
- Check that your Main Camera has a CinemachineBrain component
- Verify that the camera GameObjects are named correctly ("1st Person Camera" and "3rd Person Camera")

### First Person Mouse Look Not Working
- Ensure the first person camera is a child of the player GameObject
- Check that the CameraController is on the same GameObject as PlayerMovement
- Make sure the Input System package is properly set up

### Cameras Not Found
- Use the CameraSetupHelper to automatically create and configure cameras
- Or manually create GameObjects with the exact names: "1st Person Camera" and "3rd Person Camera"

## Customization

### Adjusting First Person Camera Position
- Select the "1st Person Camera" GameObject
- Adjust its local position to change the eye height
- Default is (0, 1.6, 0) for a standing eye level

### Modifying Third Person Camera Behavior
- Select the "3rd Person Camera" GameObject
- Adjust the CinemachineThirdPersonFollow component settings:
  - **Shoulder Offset**: Side offset from player
  - **Camera Distance**: How far back the camera sits
  - **Vertical Arm Length**: Height offset

### Changing Blend Time
- Select your Main Camera
- In the CinemachineBrain component, adjust the "Default Blend" time

## Notes

- The system automatically handles cursor locking/unlocking
- First person mode locks the cursor for mouse look
- Third person mode frees the cursor for UI interaction
- The camera switching is designed to work seamlessly during gameplay
