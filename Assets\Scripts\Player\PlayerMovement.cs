using UnityEngine;
using UnityEngine.InputSystem;

public class PlayerMovement : MonoBehaviour
{
    [Header("Movement Settings")]
    public float moveSpeed = 5f;
    public float jumpStrength = 10f;
    
    [Header("Ground Check")]
    public Transform groundCheck;
    public float groundCheckRadius = 0.2f;
    public LayerMask groundLayer;
    
    [Header("Camera")]
    public CameraController cameraController;

    [Header("Debug")]
    public bool showDebugInfo = true;

    private Rigidbody _rb;
    private bool _isGrounded;
    private Vector2 _moveInput;
    private bool _jumpInput;
    
    // Input Actions
    private InputAction moveAction;
    private InputAction jumpAction;
    
    // State Machine
    private PlayerStateMachine stateMachine;
    public IdleState IdleState { get; private set; }
    public MovingState MovingState { get; private set; }
    public JumpingState JumpingState { get; private set; }
    public FallingState FallingState { get; private set; }
    
    // Public accessors for states to use
    public Rigidbody Rb { get { return _rb; } }
    public Vector2 MoveInput { get { return _moveInput; } }
    public bool JumpInput { get { return _jumpInput; } }
    public bool IsGrounded { get { return _isGrounded; } }
    
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        _rb = GetComponent<Rigidbody>();
        
        // Prevent the player from tipping over
        _rb.freezeRotation = true;
        
        // Initialize states
        stateMachine = new PlayerStateMachine();
        IdleState = new IdleState(this, stateMachine);
        MovingState = new MovingState(this, stateMachine);
        JumpingState = new JumpingState(this, stateMachine);
        FallingState = new FallingState(this, stateMachine);
        
        // Start in idle state
        stateMachine.Initialize(IdleState);
        
        // Set up input actions
        moveAction = new InputAction(binding: "<Gamepad>/leftStick");
        moveAction.AddCompositeBinding("2DVector")
            .With("Up", "<Keyboard>/w")
            .With("Down", "<Keyboard>/s")
            .With("Left", "<Keyboard>/a")
            .With("Right", "<Keyboard>/d")
            .With("Up", "<Keyboard>/upArrow")
            .With("Down", "<Keyboard>/downArrow")
            .With("Left", "<Keyboard>/leftArrow")
            .With("Right", "<Keyboard>/rightArrow");
        
        jumpAction = new InputAction(binding: "<Keyboard>/space");
        jumpAction.AddBinding("<Gamepad>/buttonSouth");
        
        moveAction.Enable();
        jumpAction.Enable();
        
        // Create ground check if it doesn't exist
        if (groundCheck == null)
        {
            GameObject groundCheckObj = new GameObject("GroundCheck");
            groundCheckObj.transform.SetParent(transform);
            groundCheckObj.transform.localPosition = new Vector3(0, -1f, 0);
            groundCheck = groundCheckObj.transform;
        }

        // Get camera controller if not assigned
        if (cameraController == null)
        {
            cameraController = GetComponent<CameraController>();
            if (cameraController == null)
            {
                Debug.LogWarning("CameraController not found on player. Camera switching will not work.");
            }
        }
    }

    void OnEnable()
    {
        if (moveAction != null) moveAction.Enable();
        if (jumpAction != null) jumpAction.Enable();
    }

    void OnDisable()
    {
        if (moveAction != null) moveAction.Disable();
        if (jumpAction != null) jumpAction.Disable();
    }

    // Update is called once per frame
    void Update()
    {
        // Get input
        _moveInput = moveAction.ReadValue<Vector2>();
        _jumpInput = jumpAction.WasPressedThisFrame();
        
        // Check if grounded
        _isGrounded = Physics.CheckSphere(groundCheck.position, groundCheckRadius, groundLayer);
        
        // Update state machine
        stateMachine.Update();
        
        // Debug info
        if (showDebugInfo)
        {
            Debug.Log($"Current State: {stateMachine.currentState.GetType().Name}");
        }
    }
    
    void FixedUpdate()
    {
        // Let state machine handle physics
        stateMachine.FixedUpdate();
    }
    
    void Jump()
    {
        _rb.linearVelocity = new Vector3(_rb.linearVelocity.x, jumpStrength, _rb.linearVelocity.z);
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw ground check radius in scene view
        if (groundCheck != null)
        {
            Gizmos.color = _isGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(groundCheck.position, groundCheckRadius);
        }
    }
}
