{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20652, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20652, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20652, "tid": 1418, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20652, "tid": 1418, "ts": 1751300687326642, "dur": 1060, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20652, "tid": 1418, "ts": 1751300687333163, "dur": 1062, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20652, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20652, "tid": 1, "ts": 1751300686868857, "dur": 7399, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20652, "tid": 1, "ts": 1751300686876261, "dur": 50620, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20652, "tid": 1, "ts": 1751300686926902, "dur": 42597, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20652, "tid": 1418, "ts": 1751300687334231, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 20652, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686866426, "dur": 9650, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686876081, "dur": 438937, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686877202, "dur": 2992, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686880204, "dur": 1941, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882151, "dur": 351, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882509, "dur": 12, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882524, "dur": 72, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882598, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882600, "dur": 62, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882664, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882666, "dur": 35, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882705, "dur": 251, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882964, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686882967, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883059, "dur": 3, "ph": "X", "name": "ProcessMessages 2640", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883063, "dur": 87, "ph": "X", "name": "ReadAsync 2640", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883154, "dur": 2, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883157, "dur": 59, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883218, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883220, "dur": 57, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883281, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883283, "dur": 54, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883341, "dur": 2, "ph": "X", "name": "ProcessMessages 1306", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883343, "dur": 85, "ph": "X", "name": "ReadAsync 1306", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883432, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883435, "dur": 55, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883492, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883494, "dur": 41, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883537, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883538, "dur": 41, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883581, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883582, "dur": 76, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883662, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883664, "dur": 42, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883710, "dur": 1, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883712, "dur": 54, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883768, "dur": 1, "ph": "X", "name": "ProcessMessages 1114", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883770, "dur": 61, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883835, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883838, "dur": 59, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883900, "dur": 1, "ph": "X", "name": "ProcessMessages 1180", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883902, "dur": 51, "ph": "X", "name": "ReadAsync 1180", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883957, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686883959, "dur": 51, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884012, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884014, "dur": 59, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884077, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884080, "dur": 74, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884157, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884159, "dur": 67, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884230, "dur": 2, "ph": "X", "name": "ProcessMessages 1092", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884233, "dur": 58, "ph": "X", "name": "ReadAsync 1092", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884293, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884296, "dur": 92, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884391, "dur": 2, "ph": "X", "name": "ProcessMessages 1588", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884394, "dur": 42, "ph": "X", "name": "ReadAsync 1588", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884439, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884442, "dur": 54, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884497, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884499, "dur": 41, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884542, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884543, "dur": 64, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884611, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884613, "dur": 62, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884679, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884683, "dur": 68, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884753, "dur": 1, "ph": "X", "name": "ProcessMessages 1279", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884757, "dur": 36, "ph": "X", "name": "ReadAsync 1279", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884796, "dur": 50, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884848, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884849, "dur": 41, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884892, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884893, "dur": 33, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884929, "dur": 36, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884968, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686884969, "dur": 46, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885017, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885019, "dur": 35, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885056, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885058, "dur": 34, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885095, "dur": 41, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885138, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885140, "dur": 36, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885178, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885179, "dur": 35, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885218, "dur": 29, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885249, "dur": 29, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885281, "dur": 54, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885338, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885381, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885384, "dur": 49, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885434, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885436, "dur": 29, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885468, "dur": 45, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885517, "dur": 35, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885555, "dur": 40, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885598, "dur": 29, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885630, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885662, "dur": 36, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885700, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885701, "dur": 47, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885750, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885752, "dur": 32, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885786, "dur": 27, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885815, "dur": 160, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686885978, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886015, "dur": 33, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886051, "dur": 51, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886103, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886105, "dur": 75, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886185, "dur": 3, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886189, "dur": 34, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886225, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886226, "dur": 70, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886300, "dur": 52, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886354, "dur": 1, "ph": "X", "name": "ProcessMessages 1205", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886356, "dur": 31, "ph": "X", "name": "ReadAsync 1205", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886391, "dur": 35, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886429, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886465, "dur": 34, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886502, "dur": 48, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886552, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886553, "dur": 27, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886583, "dur": 41, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886627, "dur": 29, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886659, "dur": 33, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886694, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886695, "dur": 31, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886730, "dur": 33, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886765, "dur": 45, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886812, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886813, "dur": 31, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886847, "dur": 36, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886886, "dur": 28, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886917, "dur": 31, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886950, "dur": 38, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686886991, "dur": 31, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887025, "dur": 32, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887060, "dur": 35, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887098, "dur": 30, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887130, "dur": 32, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887166, "dur": 40, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887209, "dur": 29, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887241, "dur": 30, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887274, "dur": 40, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887316, "dur": 45, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887363, "dur": 1, "ph": "X", "name": "ProcessMessages 1138", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887365, "dur": 35, "ph": "X", "name": "ReadAsync 1138", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887403, "dur": 28, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887433, "dur": 35, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887471, "dur": 56, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887530, "dur": 42, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887574, "dur": 37, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887614, "dur": 32, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887649, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887685, "dur": 115, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887803, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887805, "dur": 76, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887889, "dur": 5, "ph": "X", "name": "ProcessMessages 1896", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887895, "dur": 97, "ph": "X", "name": "ReadAsync 1896", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887995, "dur": 2, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686887998, "dur": 53, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888054, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888056, "dur": 49, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888108, "dur": 1, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888111, "dur": 37, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888150, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888151, "dur": 30, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888184, "dur": 32, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888218, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888220, "dur": 33, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888255, "dur": 34, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888291, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888292, "dur": 32, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888327, "dur": 56, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888390, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888394, "dur": 58, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888454, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888456, "dur": 47, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888508, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888510, "dur": 327, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888841, "dur": 4, "ph": "X", "name": "ProcessMessages 4897", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888847, "dur": 40, "ph": "X", "name": "ReadAsync 4897", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888889, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888891, "dur": 40, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888933, "dur": 49, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888983, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686888985, "dur": 31, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889019, "dur": 28, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889051, "dur": 35, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889088, "dur": 34, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889124, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889125, "dur": 37, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889164, "dur": 41, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889208, "dur": 28, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889239, "dur": 46, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889288, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889333, "dur": 1, "ph": "X", "name": "ProcessMessages 1391", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889335, "dur": 32, "ph": "X", "name": "ReadAsync 1391", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889370, "dur": 31, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889404, "dur": 33, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889439, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889440, "dur": 34, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889476, "dur": 33, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889512, "dur": 27, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889541, "dur": 34, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889578, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889610, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889612, "dur": 32, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889646, "dur": 100, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889756, "dur": 4, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889761, "dur": 39, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889803, "dur": 3, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889807, "dur": 28, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889837, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889839, "dur": 34, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889874, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889876, "dur": 37, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889917, "dur": 40, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889958, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889959, "dur": 31, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686889992, "dur": 29, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890025, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890058, "dur": 39, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890099, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890101, "dur": 37, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890140, "dur": 29, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890172, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890205, "dur": 33, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890241, "dur": 43, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890287, "dur": 32, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890322, "dur": 27, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890370, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890411, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890413, "dur": 36, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890450, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890451, "dur": 32, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890486, "dur": 30, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890518, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890519, "dur": 28, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890551, "dur": 34, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890586, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890587, "dur": 34, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890624, "dur": 38, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890663, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890665, "dur": 38, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890706, "dur": 33, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890743, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890746, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890790, "dur": 1, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890791, "dur": 38, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890832, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890833, "dur": 45, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890881, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890883, "dur": 44, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890929, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890931, "dur": 38, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686890971, "dur": 63, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891036, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891038, "dur": 27, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891067, "dur": 30, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891100, "dur": 37, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891139, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891176, "dur": 39, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891218, "dur": 32, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891252, "dur": 29, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891284, "dur": 32, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891319, "dur": 31, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891353, "dur": 57, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891415, "dur": 3, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891419, "dur": 40, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891463, "dur": 39, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891504, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891506, "dur": 52, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891560, "dur": 39, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891601, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891603, "dur": 32, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891636, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891637, "dur": 32, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891672, "dur": 34, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891708, "dur": 31, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891742, "dur": 42, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891787, "dur": 30, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891820, "dur": 32, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686891854, "dur": 276, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892132, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892134, "dur": 77, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892213, "dur": 3, "ph": "X", "name": "ProcessMessages 4648", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892216, "dur": 49, "ph": "X", "name": "ReadAsync 4648", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892269, "dur": 64, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892338, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892340, "dur": 64, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892410, "dur": 3, "ph": "X", "name": "ProcessMessages 1145", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892414, "dur": 47, "ph": "X", "name": "ReadAsync 1145", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892463, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892465, "dur": 32, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892499, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892501, "dur": 32, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892536, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892538, "dur": 51, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892591, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892593, "dur": 35, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892629, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892631, "dur": 34, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892667, "dur": 35, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892705, "dur": 31, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892738, "dur": 45, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892785, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892787, "dur": 32, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892822, "dur": 34, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892857, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892859, "dur": 32, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892893, "dur": 34, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892931, "dur": 33, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686892967, "dur": 40, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893010, "dur": 35, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893046, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893049, "dur": 29, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893081, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893126, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893127, "dur": 63, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893192, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893193, "dur": 38, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893234, "dur": 35, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893270, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893272, "dur": 38, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893311, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893313, "dur": 27, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893342, "dur": 39, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893384, "dur": 30, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893417, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893455, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893457, "dur": 33, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893493, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893529, "dur": 31, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893563, "dur": 50, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893615, "dur": 94, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893712, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893747, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893749, "dur": 41, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893792, "dur": 76, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893870, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893906, "dur": 38, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686893947, "dur": 76, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894026, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894062, "dur": 33, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894098, "dur": 28, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894128, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894130, "dur": 64, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894196, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894232, "dur": 34, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894269, "dur": 28, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894301, "dur": 84, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894387, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894429, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894431, "dur": 36, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894470, "dur": 73, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894546, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894580, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894581, "dur": 32, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894616, "dur": 27, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894646, "dur": 66, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894714, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894749, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894751, "dur": 37, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894790, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894791, "dur": 71, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894866, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894904, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894907, "dur": 33, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894944, "dur": 29, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686894977, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895058, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895096, "dur": 33, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895135, "dur": 27, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895165, "dur": 61, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895229, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895264, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895266, "dur": 35, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895306, "dur": 65, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895377, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895419, "dur": 2, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895422, "dur": 41, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895466, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895468, "dur": 61, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895534, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895584, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895587, "dur": 31, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895620, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895622, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895692, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895812, "dur": 5, "ph": "X", "name": "ProcessMessages 1043", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895819, "dur": 57, "ph": "X", "name": "ReadAsync 1043", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895883, "dur": 3, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895887, "dur": 67, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895959, "dur": 2, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686895962, "dur": 55, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896020, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896022, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896080, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896119, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896121, "dur": 39, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896162, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896164, "dur": 71, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896239, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896291, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896293, "dur": 30, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896328, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896402, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896404, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896459, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896461, "dur": 32, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896496, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896566, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896606, "dur": 33, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896641, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896642, "dur": 30, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896675, "dur": 58, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896736, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896776, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896777, "dur": 32, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896811, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896814, "dur": 34, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896852, "dur": 69, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896923, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896925, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686896998, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897001, "dur": 46, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897050, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897054, "dur": 49, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897105, "dur": 1, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897106, "dur": 34, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897145, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897214, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897253, "dur": 33, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897290, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897292, "dur": 73, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897369, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897406, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897407, "dur": 32, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897443, "dur": 27, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897475, "dur": 34, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897512, "dur": 76, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897594, "dur": 4, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897599, "dur": 43, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897644, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897646, "dur": 76, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897725, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897768, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897770, "dur": 32, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897804, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897807, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897871, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897909, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897911, "dur": 53, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897968, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686897971, "dur": 59, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898034, "dur": 1, "ph": "X", "name": "ProcessMessages 1183", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898037, "dur": 55, "ph": "X", "name": "ReadAsync 1183", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898095, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898097, "dur": 66, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898167, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898218, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898220, "dur": 32, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898257, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898324, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898327, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898379, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898381, "dur": 31, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898418, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898484, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898543, "dur": 2, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898547, "dur": 38, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898587, "dur": 2, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898590, "dur": 40, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898634, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898638, "dur": 40, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898679, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898682, "dur": 31, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898715, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898718, "dur": 31, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898753, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898813, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898815, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898854, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898857, "dur": 32, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898891, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898894, "dur": 74, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898970, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686898973, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899006, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899007, "dur": 61, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899071, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899108, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899109, "dur": 74, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899186, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899225, "dur": 34, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899263, "dur": 27, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899292, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899294, "dur": 62, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899360, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899396, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899398, "dur": 71, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899475, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899478, "dur": 73, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899555, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899612, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899614, "dur": 33, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899649, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899652, "dur": 76, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899731, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899765, "dur": 34, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899802, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899803, "dur": 30, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899837, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899906, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899909, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899946, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899950, "dur": 41, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686899999, "dur": 78, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900081, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900124, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900126, "dur": 30, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900162, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900227, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900228, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900272, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900273, "dur": 34, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900310, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900312, "dur": 69, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900384, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900421, "dur": 57, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900482, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900486, "dur": 36, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900526, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900566, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900630, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900632, "dur": 30, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900666, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900734, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900790, "dur": 1, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900792, "dur": 33, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900827, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900898, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900935, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900937, "dur": 36, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900975, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686900977, "dur": 86, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901066, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901069, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901106, "dur": 34, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901142, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901144, "dur": 28, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901175, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901239, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901324, "dur": 4, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901329, "dur": 38, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901369, "dur": 2, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901372, "dur": 60, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901436, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901484, "dur": 1, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901486, "dur": 46, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901536, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901538, "dur": 35, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901576, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901578, "dur": 117, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901698, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901739, "dur": 4, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901744, "dur": 32, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901778, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901780, "dur": 31, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901818, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901821, "dur": 93, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901917, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901962, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901966, "dur": 32, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686901999, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902001, "dur": 87, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902091, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902123, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902125, "dur": 34, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902162, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902164, "dur": 72, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902238, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902240, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902273, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902276, "dur": 29, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902307, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902309, "dur": 69, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902383, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902432, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902433, "dur": 51, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902488, "dur": 32, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902524, "dur": 29, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902555, "dur": 73, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902631, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902671, "dur": 33, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902706, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902708, "dur": 72, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902783, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902819, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902821, "dur": 37, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902861, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902863, "dur": 77, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902947, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686902987, "dur": 30, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903022, "dur": 72, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903098, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903137, "dur": 38, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903181, "dur": 26, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903211, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903274, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903326, "dur": 1, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903328, "dur": 29, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903362, "dur": 82, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903448, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903485, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903486, "dur": 35, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903524, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903526, "dur": 75, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903605, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903656, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903658, "dur": 42, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903703, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903704, "dur": 51, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903759, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903761, "dur": 58, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903822, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903824, "dur": 53, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903885, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903889, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903949, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686903951, "dur": 122, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904079, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904082, "dur": 55, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904140, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904142, "dur": 45, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904189, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904191, "dur": 57, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904250, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904252, "dur": 36, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904291, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904294, "dur": 34, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904330, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904331, "dur": 30, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904365, "dur": 88, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904456, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904495, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904499, "dur": 56, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904562, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904565, "dur": 41, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904608, "dur": 1, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904610, "dur": 39, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904651, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904654, "dur": 34, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904689, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904691, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904754, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904809, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904813, "dur": 130, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686904950, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905022, "dur": 365, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905394, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905555, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905561, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905606, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905610, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905655, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905659, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905728, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905734, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905796, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905799, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905851, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905854, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905908, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686905911, "dur": 94, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906009, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906011, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906075, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906080, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906147, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906151, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906211, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906214, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906279, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906285, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906346, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906349, "dur": 60, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906413, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906472, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906476, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906509, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906561, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906565, "dur": 86, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906658, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906712, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906718, "dur": 50, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906773, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906776, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906824, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906828, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906903, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906906, "dur": 53, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906962, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686906965, "dur": 46, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907017, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907020, "dur": 60, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907084, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907088, "dur": 52, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907144, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907147, "dur": 81, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907233, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907237, "dur": 70, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907311, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907315, "dur": 56, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907375, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907378, "dur": 62, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907444, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907447, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907503, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907509, "dur": 54, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907566, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907569, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907627, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907630, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907677, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907681, "dur": 46, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907731, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907733, "dur": 66, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907803, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907809, "dur": 54, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907870, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907875, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907948, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686907952, "dur": 61, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908018, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908020, "dur": 56, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908083, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908087, "dur": 63, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908155, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908161, "dur": 43, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908206, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908209, "dur": 56, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908269, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908273, "dur": 55, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908334, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908339, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908405, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908409, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908453, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908455, "dur": 49, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908510, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908512, "dur": 67, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908585, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908590, "dur": 61, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908656, "dur": 3, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908661, "dur": 43, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908709, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908714, "dur": 43, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908761, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908765, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908818, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908821, "dur": 48, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908874, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908881, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908935, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686908939, "dur": 57, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909000, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909003, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909056, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909062, "dur": 39, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909104, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909108, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909149, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909152, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909195, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909198, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909252, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909255, "dur": 58, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909321, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909326, "dur": 67, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909398, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909402, "dur": 52, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909459, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909462, "dur": 38, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909503, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909508, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909550, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909553, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909599, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909603, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909654, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909657, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909711, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909715, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909760, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909763, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909796, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909798, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909855, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909858, "dur": 74, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909940, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686909945, "dur": 114, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910066, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910070, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910143, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910148, "dur": 56, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910208, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910212, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910266, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910268, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910322, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910325, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686910359, "dur": 7951, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686918319, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686918323, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686918363, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686918366, "dur": 1009, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919388, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919393, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919488, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919492, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919525, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919527, "dur": 241, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919775, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919780, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919842, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686919845, "dur": 4395, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924254, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924259, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924320, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924323, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924366, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924372, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924444, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924476, "dur": 381, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924860, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924863, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924893, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924895, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924943, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686924946, "dur": 214, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925163, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925165, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925223, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925226, "dur": 269, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925498, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925501, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925549, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925552, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925691, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925693, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925735, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686925738, "dur": 277, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926020, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926022, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926068, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926071, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926109, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926112, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926145, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926148, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926181, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926183, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926213, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926397, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926444, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926446, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926503, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926505, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926555, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926558, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926596, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926598, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926684, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926687, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926732, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926734, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926781, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926784, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926858, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926886, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926931, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926934, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686926978, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927006, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927270, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927307, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927310, "dur": 170, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927484, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927516, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927556, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927593, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927624, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927626, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927772, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927815, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927818, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927860, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927895, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927897, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927938, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927940, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927975, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686927977, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928012, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928015, "dur": 60, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928079, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928121, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928124, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928157, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928317, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928319, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928368, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928375, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928406, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928488, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928527, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928529, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928561, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928563, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928605, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928640, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928642, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928684, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928685, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928735, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928737, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928772, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928892, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928948, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928950, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928992, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686928994, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929033, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929035, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929067, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929069, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929100, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929102, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929138, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929140, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929174, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929176, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929212, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929214, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929256, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929304, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929306, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929362, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929364, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929415, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929466, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929468, "dur": 115, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929588, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929634, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929671, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929742, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929745, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929789, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929791, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929843, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929888, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929891, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686929996, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930042, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930043, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930083, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930085, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930132, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930134, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930179, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930181, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930278, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930316, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930473, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930534, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930536, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930578, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930620, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930623, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930666, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930668, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930708, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930711, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930775, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930809, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930921, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686930953, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931097, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931137, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931177, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931358, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931399, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931437, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931439, "dur": 451, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931898, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686931970, "dur": 761, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686932739, "dur": 136, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686932888, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686932896, "dur": 197, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933099, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933102, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933151, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933154, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933205, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933207, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933403, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933451, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933455, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933523, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933525, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933569, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933571, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933658, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933755, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933757, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933800, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686933802, "dur": 553, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934359, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934361, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934427, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934429, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934474, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934509, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934549, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934580, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934648, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686934678, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935016, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935061, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935064, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935168, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935201, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935233, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935389, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935457, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686935462, "dur": 890, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936358, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936361, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936392, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936393, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936502, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936542, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936546, "dur": 297, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936846, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936877, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686936982, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686937009, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686937041, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686937043, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686937077, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686937080, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686937174, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686937209, "dur": 874, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686938088, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686938090, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686938138, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686938142, "dur": 61785, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686999936, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300686999942, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687000023, "dur": 1818, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687001849, "dur": 10213, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687012076, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687012080, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687012173, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687012178, "dur": 55, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687012237, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687012239, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687012313, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687012315, "dur": 2485, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687014813, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687014817, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687014892, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687014895, "dur": 544, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687015442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687015444, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687015502, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687015505, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687015659, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687015702, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687015704, "dur": 1817, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017537, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017543, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017595, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017597, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017646, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017650, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017778, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017780, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017819, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687017821, "dur": 555, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018382, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018386, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018446, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018449, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018527, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018529, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018574, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018576, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018780, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018821, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687018823, "dur": 272, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687019100, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687019102, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687019139, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687019141, "dur": 629, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687019778, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687019781, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687019844, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687019850, "dur": 552, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687020412, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687020416, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687020494, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687020499, "dur": 523, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021033, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021037, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021122, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021127, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021271, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021274, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021383, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021385, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021603, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021608, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021665, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687021667, "dur": 345, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687022019, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687022023, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687022055, "dur": 709, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687022775, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687022778, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687022818, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687022820, "dur": 198, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023024, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023027, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023053, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023211, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023213, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023252, "dur": 564, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023825, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023881, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687023885, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024008, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024011, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024049, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024051, "dur": 563, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024620, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024623, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024695, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024697, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024743, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687024745, "dur": 974, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025731, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025736, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025874, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025879, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025928, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025930, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025964, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025966, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025995, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687025999, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026031, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026033, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026093, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026095, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026136, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026138, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026181, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026183, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026225, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026227, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026261, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026264, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026331, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026334, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026402, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026404, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026447, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026449, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026494, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026498, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026534, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026537, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026574, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026617, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026620, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026662, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026664, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026734, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026736, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026846, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026848, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026913, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687026915, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027006, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027009, "dur": 148, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027161, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027164, "dur": 79, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027251, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027256, "dur": 63, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027322, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027325, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027365, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027372, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027413, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027415, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027459, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027462, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027507, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027509, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027547, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027550, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027588, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027591, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027623, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027625, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027657, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027660, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027707, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027710, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027753, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027761, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027809, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027811, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027862, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027864, "dur": 42, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027909, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027911, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027960, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027962, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027996, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687027999, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687028112, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687028153, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687028156, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687028197, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687028199, "dur": 846, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687029057, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687029062, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687029138, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687029141, "dur": 36, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687029181, "dur": 89927, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687119121, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687119127, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687119207, "dur": 48, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687119257, "dur": 20, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687119278, "dur": 14589, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687133878, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687133883, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687133960, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687133962, "dur": 34433, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687168404, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687168408, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687168490, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687168495, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687168533, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687168535, "dur": 86129, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687254676, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687254681, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687254760, "dur": 29, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687254790, "dur": 36427, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687291228, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687291232, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687291311, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687291315, "dur": 1999, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687293324, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687293328, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687293410, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687293441, "dur": 6694, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687300145, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687300151, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687300220, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687300223, "dur": 868, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687301107, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687301112, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687301177, "dur": 32, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687301211, "dur": 566, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687301790, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687301796, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687301863, "dur": 339, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751300687302207, "dur": 12025, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20652, "tid": 1418, "ts": 1751300687334248, "dur": 1897, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20652, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20652, "tid": 8589934592, "ts": 1751300686863427, "dur": 106150, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20652, "tid": 8589934592, "ts": 1751300686969580, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20652, "tid": 8589934592, "ts": 1751300686969585, "dur": 1170, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20652, "tid": 1418, "ts": 1751300687336148, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20652, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20652, "tid": 4294967296, "ts": 1751300686841580, "dur": 474434, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20652, "tid": 4294967296, "ts": 1751300686845940, "dur": 11024, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20652, "tid": 4294967296, "ts": 1751300687316114, "dur": 6534, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20652, "tid": 4294967296, "ts": 1751300687320023, "dur": 64, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20652, "tid": 4294967296, "ts": 1751300687322734, "dur": 21, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20652, "tid": 1418, "ts": 1751300687336158, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751300686873415, "dur": 67, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751300686873510, "dur": 2681, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751300686876214, "dur": 959, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751300686877316, "dur": 99, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751300686877416, "dur": 452, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751300686879055, "dur": 770, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751300686880821, "dur": 1997, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751300686882876, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751300686883320, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751300686886421, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751300686889071, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751300686889998, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751300686890086, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751300686891816, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751300686892477, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751300686893433, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751300686896249, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751300686898806, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751300686901792, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751300686902733, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751300686904430, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751300686904805, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751300686877918, "dur": 27171, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751300686905107, "dur": 396331, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751300687301440, "dur": 264, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751300687301956, "dur": 78, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751300687302067, "dur": 1682, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751300686878164, "dur": 26957, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686905158, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686905280, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8FE2697BF98A2193.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686905856, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686905945, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686906065, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686906191, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686906254, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686906355, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686906421, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686906493, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_F8CCEDBE36EB62B5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686906672, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686906795, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686906853, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686906989, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686907123, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_308026626194FDB8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686907237, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686907355, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686907676, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686907807, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751300686907889, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686908089, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686908186, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686908246, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686908348, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686908523, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751300686908779, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686909008, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686909189, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751300686909363, "dur": 371, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686909871, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686909996, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686910084, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10719215101466552486.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686910197, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686910516, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751300686910569, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686911970, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686913610, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686914116, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686914336, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686915019, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686915291, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686915523, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686916137, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686916392, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686916663, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686916963, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686917284, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686917532, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686917841, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686918094, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686918343, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686918721, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686919486, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686919916, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686920278, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686920715, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686921047, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686921355, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686921634, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686922691, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686923200, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686923563, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686924560, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686925259, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686925485, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751300686927769, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686927900, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686928054, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686928264, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686928344, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751300686930705, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686930832, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686930907, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686931093, "dur": 1287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751300686932382, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686932450, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686932518, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751300686932721, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751300686933301, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686933402, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300686933471, "dur": 76122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687009616, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751300687012271, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687012365, "dur": 2733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751300687015100, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687015181, "dur": 3683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751300687018865, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687019104, "dur": 3870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751300687022976, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687023060, "dur": 4425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751300687027486, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687028043, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751300687028106, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687028206, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687028285, "dur": 97404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687125719, "dur": 39490, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751300687125691, "dur": 41147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751300687168223, "dur": 271, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751300687169404, "dur": 85512, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751300687290943, "dur": 9420, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751300687290927, "dur": 9443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751300687300396, "dur": 982, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751300686878396, "dur": 26775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686905183, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686905865, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686905936, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686906052, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686906126, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686906189, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686906285, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686906446, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_44462716F1E6187F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686906523, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686906611, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686906725, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686906850, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C047F56604413718.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686906981, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686907042, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686907153, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686907262, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686907372, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686907556, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686907618, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686907755, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686907970, "dur": 10541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751300686918608, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686918704, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686918983, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686919278, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686919642, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686920055, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686920537, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686921075, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686921377, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686921623, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686922260, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686922950, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686923354, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686923932, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686924582, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686925270, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686925483, "dur": 908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751300686926393, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686926528, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686926779, "dur": 2012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751300686928792, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686928939, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686929064, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686929380, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751300686930175, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686930435, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686930814, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686931498, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686931712, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751300686932235, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686932455, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751300686932529, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686932604, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686932765, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686932841, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751300686933724, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686933840, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686934051, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751300686934580, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686934711, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751300686934900, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751300686935553, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300686935622, "dur": 73997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687009621, "dur": 2645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751300687012268, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687012488, "dur": 3167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751300687015656, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687015978, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751300687018709, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687019100, "dur": 6511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751300687025612, "dur": 791, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687026424, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687026731, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687026904, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687027178, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687027314, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687027452, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751300687027795, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687028011, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687028068, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751300687028125, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751300687028475, "dur": 272959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686878257, "dur": 26885, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686905158, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686905307, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686905992, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686906054, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686906133, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A587944D9110748A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686906241, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A587944D9110748A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686906341, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_5F13DCF03A10B823.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686906419, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686906485, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F697CEAF687CB7A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686906607, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686906671, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686906798, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_595F986B2A245834.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686906984, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686907112, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686907217, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CBABBF352E91654E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686907322, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686907376, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686907453, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686907607, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686907836, "dur": 11749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751300686919668, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686919755, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686919972, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686920081, "dur": 4374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751300686924565, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686924645, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686924770, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751300686925268, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686925509, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751300686926456, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686926874, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686927183, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751300686927863, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686927934, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686928184, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751300686929165, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686929388, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686929674, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751300686930626, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686931009, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686931519, "dur": 2327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686933847, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751300686934075, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751300686934607, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686934681, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300686934773, "dur": 74849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300687009631, "dur": 2648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751300687012333, "dur": 2617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751300687014952, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300687015076, "dur": 3512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751300687018597, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300687018686, "dur": 4284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751300687022973, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300687023056, "dur": 2601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751300687025659, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300687025995, "dur": 3199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751300687029196, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751300687029311, "dur": 272134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686878312, "dur": 26842, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686905166, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686905900, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F0EAC96F0603CD08.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686906117, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686906285, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686906490, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_467B09015CCE177C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686906640, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_0E07881F85CDD302.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686906703, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686906766, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8EA224AC6679B7C2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686907002, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686907108, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_EE062F8861099912.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686907212, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_584BDBB0AACB340C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686907340, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686907527, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686908081, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751300686908181, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686908263, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751300686908682, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751300686908884, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751300686909039, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686909223, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751300686909385, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751300686909456, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751300686909568, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751300686910092, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686910182, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686910438, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751300686910652, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686912031, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686913553, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686914024, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686914258, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686914563, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686914853, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686915282, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686915526, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686916140, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686916589, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686916848, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686917152, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686917956, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686918218, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686918458, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686918808, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686919074, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686919320, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686919621, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686920067, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686920545, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686920907, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686921186, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686921472, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686921720, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686922392, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686923110, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686923624, "dur": 937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686924562, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686925315, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686925475, "dur": 872, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686926355, "dur": 1123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751300686927479, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686928057, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686928248, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751300686929156, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686929641, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686929785, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751300686930063, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751300686930709, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686930811, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751300686931481, "dur": 220, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300686932293, "dur": 67913, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751300687009587, "dur": 2668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751300687012264, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687012407, "dur": 2543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751300687014952, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687015127, "dur": 2542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751300687017671, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687017827, "dur": 2658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751300687020487, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687020680, "dur": 2602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751300687023343, "dur": 2734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751300687026676, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687026858, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687026923, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687027007, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687027537, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687027830, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687028113, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687028452, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751300687028515, "dur": 272971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686878431, "dur": 26754, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686905192, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686905824, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686905891, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686905971, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686906061, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686906146, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686906201, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686906283, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686906414, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8A89D5AFAE49E63E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686906542, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E681FA607883332E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686906635, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_15A0A72BE0744047.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686906757, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_33AFB99E7761AA5C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686906823, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686906978, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686907076, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686907196, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686907306, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7E691CD5ECB673D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686907464, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686907595, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686907820, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751300686907982, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686908059, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751300686908634, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751300686908742, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751300686908802, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686908977, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751300686909063, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686909349, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686909773, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751300686909956, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751300686910086, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751300686910233, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686910539, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686911628, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686913075, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686913263, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686913486, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686913993, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686914269, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686914580, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686915116, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686915353, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686915594, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686916307, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686916575, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686917150, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686917400, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686917689, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686918000, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686918282, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686918808, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686919077, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686919352, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686919833, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686920430, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686920927, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686921181, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686921480, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686921910, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686922702, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686922982, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686923387, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686924085, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686924553, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686925282, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686925470, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686925569, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751300686926207, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686926373, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686926465, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686926722, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686927010, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686927233, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751300686927962, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686928266, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686928338, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686928396, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751300686929135, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686929267, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686929434, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686929550, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686929950, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686930828, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686931526, "dur": 5851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300686937379, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751300686937522, "dur": 72107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300687009630, "dur": 2646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751300687012277, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300687012400, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751300687014952, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300687015085, "dur": 3622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751300687018709, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300687018832, "dur": 8091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751300687026924, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300687027574, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300687028148, "dur": 1156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751300687029307, "dur": 272125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686878466, "dur": 26737, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686905215, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_77F91A8BF54EFB7C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686905976, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686906165, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686906260, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686906379, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686906447, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686906568, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686906679, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686906754, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686906862, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7E47E9336205E31D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686907080, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686907203, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_48483D327EA17808.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686907510, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686907588, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686907702, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686907831, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686907914, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686908089, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751300686908249, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686908377, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686908487, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686908567, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751300686908693, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686908808, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686909121, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686909271, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686909433, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686909569, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686909761, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686909923, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686910031, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686910243, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686910426, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751300686910546, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686912107, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686913219, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686913492, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686913854, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686914172, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686914422, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686915023, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686915514, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686916131, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686916383, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686916673, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686916941, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686917233, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686917608, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686917932, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686918197, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686918468, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686918745, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686918999, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686919268, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686919578, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686920196, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686920589, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686921040, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686921372, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686921729, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686922523, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686923009, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686923401, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686924021, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686924579, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686925278, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686925500, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300686926473, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686926706, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686926819, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300686928300, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686928656, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686929208, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300686930319, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686930384, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686930600, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300686931083, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686931243, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686931502, "dur": 2290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686933794, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686933983, "dur": 1461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300686935552, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686935692, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300686936592, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686936682, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686936824, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300686937313, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686937372, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751300686937501, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300686938311, "dur": 64, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300686939428, "dur": 179957, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300687126106, "dur": 7839, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751300687125672, "dur": 8357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751300687134031, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300687134107, "dur": 31102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751300687134103, "dur": 32829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751300687168612, "dur": 203, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751300687169744, "dur": 85292, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751300687290929, "dur": 518, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751300687290918, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751300687291517, "dur": 9997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686878502, "dur": 26723, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686905239, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_1A2A3B79BD3B4CFD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686905970, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686906068, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686906192, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686906292, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686906593, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686906663, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686906742, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DAD9D37CE1A4BA55.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686906857, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4E1AB9DAA17812DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686906988, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686907075, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686907335, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686907437, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_E106F825F731F4BC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686907596, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686907858, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751300686907935, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751300686908142, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751300686908198, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686908290, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751300686908578, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751300686908694, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751300686909019, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686909192, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751300686909308, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751300686909412, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751300686909491, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686909655, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686909825, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686910122, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686910513, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686910865, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686912081, "dur": 1610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686913691, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686913990, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686914254, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686914566, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686914902, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686915236, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686915477, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686916148, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686916517, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686916779, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686917295, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686917579, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686917887, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686918129, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686918425, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686918985, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686919220, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686919789, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686920394, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686920684, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686921110, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686921433, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686921738, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686922570, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686923148, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686923444, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686923559, "dur": 989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686924548, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686925269, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686925449, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686925579, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751300686926282, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686926427, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686926505, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686926787, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686927175, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751300686927850, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686927928, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686928457, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751300686929386, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686929517, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686929788, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686930090, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686930159, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751300686930862, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686930985, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686931522, "dur": 5170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300686936694, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751300686936844, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751300686937175, "dur": 72498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300687009675, "dur": 2597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751300687012273, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300687012351, "dur": 2603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751300687014955, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300687015116, "dur": 2702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751300687017819, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300687017930, "dur": 4226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751300687022158, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300687022325, "dur": 2627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751300687024953, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300687025057, "dur": 4207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751300687029389, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751300687029459, "dur": 272040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686878557, "dur": 26689, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686905258, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686905779, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686905854, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686905967, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686906130, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686906240, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686906318, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686906382, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686906456, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686906575, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8DE949524442C9D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686906853, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9D64899DE49D3B06.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686907050, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686907128, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7B8E45163E4907D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686907248, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686907358, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1948650C4D21EE20.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686907430, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686907880, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751300686908190, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686908255, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751300686908614, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751300686908957, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751300686909216, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686909516, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686909680, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751300686909986, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751300686910091, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686910466, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751300686910608, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686912122, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686913590, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686914167, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686914438, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686914923, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686915240, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686915486, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686916141, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686916402, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686916715, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686916988, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686917275, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686917529, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686917853, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686918117, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686918349, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686918661, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686918942, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686919195, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686919500, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686919858, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686920227, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686920730, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686921113, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686921682, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686922376, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686923138, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686923567, "dur": 976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686924579, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686925479, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686925694, "dur": 1684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751300686927379, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686927595, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751300686928274, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686928326, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751300686928381, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686928589, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686928668, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751300686929428, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686929508, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686929581, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686929809, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686930092, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686930176, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751300686930711, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686930817, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686930876, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686931496, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751300686931676, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751300686932184, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686932264, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686932354, "dur": 38691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686973029, "dur": 301, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1751300686973331, "dur": 1126, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 8, "ts": 1751300686974457, "dur": 87, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 8, "ts": 1751300686971047, "dur": 3506, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300686974554, "dur": 35057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300687009615, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751300687012271, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300687012422, "dur": 2540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751300687014964, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300687015122, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751300687017673, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300687017804, "dur": 3707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751300687021513, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300687021589, "dur": 2988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751300687024578, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300687024925, "dur": 3457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751300687028451, "dur": 262477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751300687290955, "dur": 498, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751300687290930, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751300687291508, "dur": 2152, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751300687293673, "dur": 7767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686878601, "dur": 26670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686905274, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BB587A5119495C88.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686905887, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686905959, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686906122, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_228E07120E318326.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686906231, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686906320, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686906416, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686906522, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_69FC355D1BD4BB29.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686906625, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686906682, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686906745, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_76EB92ADF25C401C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686906992, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686907072, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686907229, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686907375, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686907454, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686907635, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751300686907949, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686908243, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751300686908354, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751300686908450, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751300686908672, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751300686909126, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686909235, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686910007, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751300686910089, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751300686910230, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686910545, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686912057, "dur": 1412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686913470, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686913829, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686914192, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686914447, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686914922, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686915267, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686915506, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686916129, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686916382, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686916617, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686916866, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686917220, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686917547, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686917874, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686918114, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686918783, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686919055, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686919318, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686919616, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686919950, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686920349, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686920644, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686920922, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686921176, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686921497, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686921777, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686922408, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686923178, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686923561, "dur": 988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686924550, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686925266, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686925478, "dur": 529, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686926010, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751300686926818, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686926994, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686927180, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751300686927847, "dur": 953, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686928859, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686929014, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686929494, "dur": 887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751300686930440, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686930813, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686930916, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686931102, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751300686932396, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686932477, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751300686932538, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686932593, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686932766, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751300686933631, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686933728, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686933837, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686934065, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751300686934719, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300686934835, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751300686934974, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751300686935370, "dur": 74219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300687009592, "dur": 2625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751300687012219, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300687012305, "dur": 2657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751300687014964, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300687015080, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751300687017671, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300687017783, "dur": 3154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751300687020938, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300687021297, "dur": 2752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751300687024050, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300687024130, "dur": 3566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751300687027956, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300687028074, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751300687028229, "dur": 1116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751300687029396, "dur": 272065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686878649, "dur": 26631, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686905282, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686906020, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686906087, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686906206, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686906310, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686906432, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686906547, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686906829, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686906984, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686907118, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5DFD03B93DA29B4D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686907180, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686907244, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_815CA55F1B3FC2AD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686907377, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_D4344483B74B03A7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686907808, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751300686907986, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686908044, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751300686908206, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686908264, "dur": 439, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751300686908906, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751300686909268, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751300686909381, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751300686909558, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686909638, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751300686909975, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751300686910227, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686910485, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686910879, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686912496, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686912679, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686912877, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686913063, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686913277, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686913516, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686914048, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686914312, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686914794, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686915124, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686915417, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686916049, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686916321, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686916589, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686916864, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686917181, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686917468, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686917740, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686918014, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686918283, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686918624, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686918879, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686919132, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686919387, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686919799, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686920465, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686920931, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686921189, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686921482, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686921799, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686922477, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686922945, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686923560, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686924575, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686925285, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686925491, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751300686926162, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686926386, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686926470, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686926718, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686927089, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751300686928092, "dur": 582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686928688, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686928862, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686928997, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686929080, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686929365, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686929446, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751300686930252, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686930489, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686930856, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686931501, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751300686931734, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751300686932345, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686932421, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300686932524, "dur": 77054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300687009581, "dur": 2726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751300687012310, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300687012468, "dur": 2637, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751300687015109, "dur": 4843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751300687019954, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300687020051, "dur": 4139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751300687024192, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300687024326, "dur": 4057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751300687028461, "dur": 262492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751300687290985, "dur": 462, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751300687290954, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751300687291512, "dur": 9938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686878713, "dur": 26575, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686905290, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1FE2264601546D0E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686905969, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686906074, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686906381, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686906460, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8325F75A1495950D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686906539, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686906603, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0333895C18D609C9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686906839, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686906998, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_0EEB0D491376C433.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686907082, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686907185, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_C1BEC5DDD68DA9AF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686907314, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686907653, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686907955, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686908045, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751300686908240, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751300686908528, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751300686908777, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751300686908908, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751300686908965, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686909131, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751300686909546, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751300686909817, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686909949, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751300686910075, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686910179, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686910540, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686911985, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686913679, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686914054, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686914353, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686914805, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686915157, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686915412, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686916121, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686916421, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686916762, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686917098, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686917344, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686917656, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686917947, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686918194, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686918709, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686918983, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686919227, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686919587, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686919897, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686920343, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686920690, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686921054, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686921515, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686922240, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686922931, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686923186, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686923564, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686924592, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686925275, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686925512, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751300686926483, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686926873, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686927297, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686927590, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751300686928259, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686928329, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686928391, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686928863, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686929003, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751300686929914, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686929974, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686930860, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686931494, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751300686931684, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686931744, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751300686932360, "dur": 42203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300686974564, "dur": 35032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300687009599, "dur": 2664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751300687012275, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300687012456, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751300687012578, "dur": 2521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751300687015149, "dur": 597, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751300687015751, "dur": 3092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751300687018845, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300687019423, "dur": 3985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751300687023411, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300687023544, "dur": 3711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751300687027257, "dur": 905, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300687028193, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300687028262, "dur": 1121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751300687029409, "dur": 272020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686878757, "dur": 26526, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686905285, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B1D6DCAB2D5A18DA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686905982, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686906057, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686906187, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686906262, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686906395, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686906509, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0999ABDFA115F312.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686906643, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1DC8148833156840.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686906713, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686906771, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_51B6A25823037467.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686906836, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686907165, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686907271, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686907456, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686907644, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686907718, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686907933, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751300686908108, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751300686908326, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751300686908383, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751300686908882, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751300686908984, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751300686909182, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751300686909321, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751300686909683, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751300686909758, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751300686909998, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751300686910076, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686910197, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686910401, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751300686910521, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751300686910632, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686912282, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686913286, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686913520, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686913946, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686914250, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686914643, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686915116, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686915380, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686915606, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686916239, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686916597, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686916880, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686917195, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686917448, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686917747, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686918060, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686918330, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686918642, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686918911, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686919156, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686919594, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686920147, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686920488, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686920873, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686921181, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686921495, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686921852, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686922456, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686923157, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686923405, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686923566, "dur": 998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686924564, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686925256, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686925523, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751300686926247, "dur": 798, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686927089, "dur": 697, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686927811, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686928171, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686928241, "dur": 1350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751300686929604, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686929782, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686930067, "dur": 1297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751300686931365, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686931514, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686931727, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751300686932341, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686932448, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686932623, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751300686933630, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686933793, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751300686933998, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751300686934453, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686934711, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300686934830, "dur": 74744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300687009578, "dur": 2712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751300687012292, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300687012362, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751300687014952, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300687015046, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751300687015177, "dur": 2492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751300687017671, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300687018105, "dur": 3477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751300687021583, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300687021928, "dur": 2780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751300687024710, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300687024950, "dur": 4307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751300687029258, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751300687029395, "dur": 272071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751300687310906, "dur": 2259, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20652, "tid": 1418, "ts": 1751300687336727, "dur": 2577, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20652, "tid": 1418, "ts": 1751300687339349, "dur": 3523, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20652, "tid": 1418, "ts": 1751300687331379, "dur": 12939, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}