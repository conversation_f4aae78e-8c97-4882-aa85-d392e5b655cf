using UnityEngine;
using Unity.Cinemachine;

/// <summary>
/// Helper script to set up the camera system for first and third person views.
/// This script can be run in the editor to automatically configure your cameras.
/// </summary>
public class CameraSetupHelper : MonoBehaviour
{
    [Header("Setup Configuration")]
    [SerializeField] private Transform playerTransform;
    [SerializeField] private bool autoSetupOnStart = false;
    
    [<PERSON><PERSON>("Camera Settings")]
    [SerializeField] private float firstPersonHeight = 1.6f;
    [SerializeField] private Vector3 thirdPersonOffset = new Vector3(0, 2f, -5f);
    
    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = true;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupCameras();
        }
    }
    
    [ContextMenu("Setup Cameras")]
    public void SetupCameras()
    {
        if (playerTransform == null)
        {
            // Try to find the player
            GameObject player = GameObject.FindWithTag("Player");
            if (player == null)
            {
                player = GameObject.Find("Player");
            }
            
            if (player != null)
            {
                playerTransform = player.transform;
            }
            else
            {
                Debug.LogError("Player not found! Please assign the player transform or tag your player GameObject with 'Player' tag.");
                return;
            }
        }
        
        // Setup First Person Camera
        SetupFirstPersonCamera();
        
        // Setup Third Person Camera
        SetupThirdPersonCamera();
        
        // Ensure Main Camera has CinemachineBrain
        SetupMainCamera();
        
        Debug.Log("Camera setup completed!");
    }
    
    void SetupFirstPersonCamera()
    {
        // Look for existing first person camera
        GameObject fpCameraObj = GameObject.Find("1st Person Camera");
        
        if (fpCameraObj == null)
        {
            // Create new first person camera
            fpCameraObj = new GameObject("1st Person Camera");
            fpCameraObj.transform.SetParent(playerTransform);
            fpCameraObj.transform.localPosition = new Vector3(0, firstPersonHeight, 0);
            fpCameraObj.transform.localRotation = Quaternion.identity;
        }
        
        // Add CinemachineCamera component if it doesn't exist
        CinemachineCamera fpCamera = fpCameraObj.GetComponent<CinemachineCamera>();
        if (fpCamera == null)
        {
            fpCamera = fpCameraObj.AddComponent<CinemachineCamera>();
        }
        
        // Configure the first person camera
        fpCamera.Priority = 0; // Start inactive
        fpCamera.Follow = null; // First person doesn't need follow
        fpCamera.LookAt = null; // First person doesn't need look at
        
        // Set lens properties for first person
        var lens = fpCamera.Lens;
        lens.FieldOfView = 75f; // Slightly wider FOV for first person
        fpCamera.Lens = lens;
        
        if (showDebugInfo)
        {
            Debug.Log("First Person Camera configured");
        }
    }
    
    void SetupThirdPersonCamera()
    {
        // Look for existing third person camera (might be named "FreeLook Camera")
        GameObject tpCameraObj = GameObject.Find("3rd Person Camera");
        if (tpCameraObj == null)
        {
            tpCameraObj = GameObject.Find("FreeLook Camera");
        }
        
        if (tpCameraObj == null)
        {
            // Create new third person camera
            tpCameraObj = new GameObject("3rd Person Camera");
            tpCameraObj.transform.position = playerTransform.position + thirdPersonOffset;
        }
        else
        {
            // Rename FreeLook Camera to 3rd Person Camera for consistency
            tpCameraObj.name = "3rd Person Camera";
        }
        
        // Add CinemachineCamera component if it doesn't exist
        CinemachineCamera tpCamera = tpCameraObj.GetComponent<CinemachineCamera>();
        if (tpCamera == null)
        {
            tpCamera = tpCameraObj.AddComponent<CinemachineCamera>();
        }
        
        // Configure the third person camera
        tpCamera.Priority = 10; // Start active
        tpCamera.Follow = playerTransform;
        tpCamera.LookAt = playerTransform;
        
        // Add third person follow component if it doesn't exist
        CinemachineThirdPersonFollow tpFollow = tpCameraObj.GetComponent<CinemachineThirdPersonFollow>();
        if (tpFollow == null)
        {
            tpFollow = tpCameraObj.AddComponent<CinemachineThirdPersonFollow>();
        }
        
        // Configure third person follow settings
        tpFollow.ShoulderOffset = new Vector3(1f, 0f, 0f);
        tpFollow.VerticalArmLength = 1.5f;
        tpFollow.CameraSide = 1f; // Right shoulder
        tpFollow.CameraDistance = 5f;
        
        // Add rotation composer for look at functionality
        CinemachineRotationComposer rotComposer = tpCameraObj.GetComponent<CinemachineRotationComposer>();
        if (rotComposer == null)
        {
            rotComposer = tpCameraObj.AddComponent<CinemachineRotationComposer>();
        }
        
        if (showDebugInfo)
        {
            Debug.Log("Third Person Camera configured");
        }
    }
    
    void SetupMainCamera()
    {
        // Find main camera
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            GameObject mainCameraObj = GameObject.Find("Main Camera");
            if (mainCameraObj != null)
            {
                mainCamera = mainCameraObj.GetComponent<Camera>();
            }
        }
        
        if (mainCamera == null)
        {
            Debug.LogError("Main Camera not found!");
            return;
        }
        
        // Add CinemachineBrain if it doesn't exist
        CinemachineBrain brain = mainCamera.GetComponent<CinemachineBrain>();
        if (brain == null)
        {
            brain = mainCamera.gameObject.AddComponent<CinemachineBrain>();
        }
        
        // Configure brain settings
        brain.DefaultBlend.Style = CinemachineBlendDefinition.Styles.EaseInOut;
        brain.DefaultBlend.Time = 1f; // 1 second blend time
        
        if (showDebugInfo)
        {
            Debug.Log("Main Camera with CinemachineBrain configured");
        }
    }
    
    [ContextMenu("Find Player")]
    public void FindPlayer()
    {
        GameObject player = GameObject.FindWithTag("Player");
        if (player == null)
        {
            player = GameObject.Find("Player");
        }
        
        if (player != null)
        {
            playerTransform = player.transform;
            Debug.Log($"Player found: {player.name}");
        }
        else
        {
            Debug.LogWarning("Player not found! Make sure your player GameObject is named 'Player' or has the 'Player' tag.");
        }
    }
}
