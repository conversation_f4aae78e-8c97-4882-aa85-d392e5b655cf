using UnityEngine;
using UnityEngine.InputSystem;
using Unity.Cinemachine;

public class CameraController : MonoBehaviour
{
    [Header("Camera References")]
    [SerializeField] private CinemachineCamera firstPersonCamera;
    [SerializeField] private CinemachineCamera thirdPersonCamera;
    
    [<PERSON>er("Mouse Look Settings")]
    [SerializeField] private float mouseSensitivity = 100f;
    [SerializeField] private float verticalLookLimit = 90f;
    
    [Header("Camera Switching")]
    [SerializeField] private KeyCode switchCameraKey = KeyCode.C;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    
    // Private variables
    private Vector2 _lookInput;
    private float _xRotation = 0f;
    private bool _isFirstPerson = false;
    private Transform _playerTransform;
    
    // Input Actions
    private InputAction lookAction;
    private InputAction switchCameraAction;
    
    // Camera priorities
    private const int ACTIVE_CAMERA_PRIORITY = 10;
    private const int INACTIVE_CAMERA_PRIORITY = 0;
    
    void Start()
    {
        _playerTransform = transform;
        
        // Find cameras if not assigned
        if (firstPersonCamera == null)
            firstPersonCamera = GameObject.Find("1st Person Camera")?.GetComponent<CinemachineCamera>();
        
        if (thirdPersonCamera == null)
            thirdPersonCamera = GameObject.Find("3rd Person Camera")?.GetComponent<CinemachineCamera>();
        
        // Validate camera references
        if (firstPersonCamera == null)
        {
            Debug.LogError("1st Person Camera not found! Please assign it in the inspector or create a camera named '1st Person Camera'");
        }
        
        if (thirdPersonCamera == null)
        {
            Debug.LogError("3rd Person Camera not found! Please assign it in the inspector or create a camera named '3rd Person Camera'");
        }
        
        // Set up input actions
        SetupInputActions();
        
        // Initialize camera state (start with 3rd person)
        SwitchToThirdPerson();
        
        // Lock cursor for first person mode
        UpdateCursorState();
    }
    
    void SetupInputActions()
    {
        // Mouse look input
        lookAction = new InputAction(binding: "<Mouse>/delta");
        lookAction.AddBinding("<Gamepad>/rightStick");
        
        // Camera switch input
        switchCameraAction = new InputAction(binding: $"<Keyboard>/{switchCameraKey.ToString().ToLower()}");
        switchCameraAction.AddBinding("<Gamepad>/buttonNorth"); // Y button on Xbox controller
        
        lookAction.Enable();
        switchCameraAction.Enable();
        
        // Subscribe to switch camera input
        switchCameraAction.performed += OnSwitchCamera;
    }
    
    void OnEnable()
    {
        if (lookAction != null) lookAction.Enable();
        if (switchCameraAction != null) switchCameraAction.Enable();
    }
    
    void OnDisable()
    {
        if (lookAction != null) lookAction.Disable();
        if (switchCameraAction != null) switchCameraAction.Disable();
    }
    
    void OnDestroy()
    {
        if (switchCameraAction != null)
            switchCameraAction.performed -= OnSwitchCamera;
    }
    
    void Update()
    {
        // Handle mouse look only in first person
        if (_isFirstPerson && firstPersonCamera != null)
        {
            HandleMouseLook();
        }
        
        // Debug info
        if (showDebugInfo)
        {
            Debug.Log($"Camera Mode: {(_isFirstPerson ? "First Person" : "Third Person")}");
            if (_isFirstPerson && _lookInput.magnitude > 0.01f)
            {
                Debug.Log($"Mouse Input: {_lookInput}, Player Y Rotation: {_playerTransform.eulerAngles.y}, Camera X Rotation: {_xRotation}");
            }
        }
    }
    
    void HandleMouseLook()
    {
        // Get mouse input
        _lookInput = lookAction.ReadValue<Vector2>();

        // Apply sensitivity and time scaling
        _lookInput *= mouseSensitivity * Time.deltaTime;

        // Rotate the player horizontally (yaw)
        _playerTransform.Rotate(Vector3.up * _lookInput.x);

        // Handle vertical rotation (pitch) - clamp to prevent over-rotation
        _xRotation -= _lookInput.y;
        _xRotation = Mathf.Clamp(_xRotation, -verticalLookLimit, verticalLookLimit);

        // Apply vertical rotation to the first person camera
        if (firstPersonCamera != null)
        {
            // Set the camera's local rotation to only handle pitch (vertical look)
            // The yaw (horizontal) is handled by rotating the player transform above
            firstPersonCamera.transform.localRotation = Quaternion.Euler(_xRotation, 0f, 0f);
        }
    }
    
    void OnSwitchCamera(InputAction.CallbackContext context)
    {
        if (_isFirstPerson)
        {
            SwitchToThirdPerson();
        }
        else
        {
            SwitchToFirstPerson();
        }
    }
    
    public void SwitchToFirstPerson()
    {
        _isFirstPerson = true;

        if (firstPersonCamera != null)
        {
            firstPersonCamera.Priority = ACTIVE_CAMERA_PRIORITY;

            // Reset the camera's local rotation to ensure clean first person view
            // Keep any existing X rotation but reset Y and Z
            Vector3 currentRotation = firstPersonCamera.transform.localEulerAngles;
            _xRotation = currentRotation.x;
            // Normalize the X rotation to be between -180 and 180
            if (_xRotation > 180f) _xRotation -= 360f;
            firstPersonCamera.transform.localRotation = Quaternion.Euler(_xRotation, 0f, 0f);
        }

        if (thirdPersonCamera != null)
        {
            thirdPersonCamera.Priority = INACTIVE_CAMERA_PRIORITY;
        }

        UpdateCursorState();

        if (showDebugInfo)
        {
            Debug.Log("Switched to First Person Camera");
        }
    }
    
    public void SwitchToThirdPerson()
    {
        _isFirstPerson = false;
        
        if (thirdPersonCamera != null)
        {
            thirdPersonCamera.Priority = ACTIVE_CAMERA_PRIORITY;
        }
        
        if (firstPersonCamera != null)
        {
            firstPersonCamera.Priority = INACTIVE_CAMERA_PRIORITY;
        }
        
        UpdateCursorState();
        
        if (showDebugInfo)
        {
            Debug.Log("Switched to Third Person Camera");
        }
    }
    
    void UpdateCursorState()
    {
        if (_isFirstPerson)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
        else
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
    }
    
    // Public getters for other scripts
    public bool IsFirstPerson => _isFirstPerson;
    public CinemachineCamera FirstPersonCamera => firstPersonCamera;
    public CinemachineCamera ThirdPersonCamera => thirdPersonCamera;
    
    // Public methods for external control
    public void SetMouseSensitivity(float sensitivity)
    {
        mouseSensitivity = sensitivity;
    }

    public void ToggleCamera()
    {
        OnSwitchCamera(new InputAction.CallbackContext());
    }

    // Debug method to test rotation manually
    [ContextMenu("Test Horizontal Rotation")]
    public void TestHorizontalRotation()
    {
        if (_isFirstPerson)
        {
            _playerTransform.Rotate(Vector3.up * 45f);
            Debug.Log($"Rotated player by 45 degrees. New Y rotation: {_playerTransform.eulerAngles.y}");
        }
        else
        {
            Debug.Log("Switch to first person mode first to test rotation");
        }
    }
}
